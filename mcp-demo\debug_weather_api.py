#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调试和风天气 API 调用问题
"""

import asyncio
import httpx
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# 加载环境变量
dotenv_path = Path(__file__).parent / '.env'
load_dotenv(dotenv_path)

QWEATHER_API_BASE = os.getenv("QWEATHER_API_BASE")
QWEATHER_API_KEY = os.getenv("QWEATHER_API_KEY")

async def test_qweather_api():
    """测试和风天气 API 的各种调用方式"""
    
    print("=== 和风天气 API 调试 ===\n")
    print(f"API Base: {QWEATHER_API_BASE}")
    print(f"API Key: {QWEATHER_API_KEY}")
    print()
    
    # 测试参数
    location = "101010100"  # 北京
    
    # 测试 1: 天气预警 API
    print("测试 1: 天气预警 API")
    await test_warning_api(location)
    
    print("\n" + "="*50 + "\n")
    
    # 测试 2: 天气预报 API
    print("测试 2: 天气预报 API")
    await test_forecast_api(location)
    
    print("\n" + "="*50 + "\n")
    
    # 测试 3: 使用不同的认证方式
    print("测试 3: 测试不同认证方式")
    await test_different_auth_methods(location)

async def test_warning_api(location: str):
    """测试天气预警 API"""
    
    endpoint = "warning/now"
    url = f"{QWEATHER_API_BASE}/{endpoint}"
    
    params = {
        "location": location,
        "lang": "zh",
        "key": QWEATHER_API_KEY
    }
    
    print(f"请求 URL: {url}")
    print(f"请求参数: {params}")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=params, timeout=30.0)
            print(f"响应状态码: {response.status_code}")
            print(f"响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应数据: {data}")
                
                if data.get("code") == "200":
                    warnings = data.get("warning", [])
                    if warnings:
                        print(f"✅ 成功获取 {len(warnings)} 条预警信息")
                        for i, warning in enumerate(warnings):
                            print(f"预警 {i+1}: {warning.get('title', '未知')}")
                    else:
                        print("✅ API 调用成功，但当前没有预警信息")
                else:
                    print(f"❌ API 返回错误码: {data.get('code')}")
                    print(f"错误信息: {data}")
            else:
                print(f"❌ HTTP 错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

async def test_forecast_api(location: str):
    """测试天气预报 API"""
    
    endpoint = "weather/3d"
    url = f"{QWEATHER_API_BASE}/{endpoint}"
    
    params = {
        "location": location,
        "lang": "zh",
        "key": QWEATHER_API_KEY
    }
    
    print(f"请求 URL: {url}")
    print(f"请求参数: {params}")
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=params, timeout=30.0)
            print(f"响应状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"响应代码: {data.get('code')}")
                
                if data.get("code") == "200":
                    daily = data.get("daily", [])
                    print(f"✅ 成功获取 {len(daily)} 天预报数据")
                else:
                    print(f"❌ API 返回错误码: {data.get('code')}")
                    print(f"错误信息: {data}")
            else:
                print(f"❌ HTTP 错误: {response.status_code}")
                print(f"响应内容: {response.text}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")

async def test_different_auth_methods(location: str):
    """测试不同的认证方式"""
    
    print("测试方式 1: API Key 作为查询参数")
    await test_with_api_key_param(location)
    
    print("\n测试方式 2: JWT Bearer Token（如果有的话）")
    await test_with_bearer_token(location)

async def test_with_api_key_param(location: str):
    """使用 API Key 作为查询参数"""
    
    url = f"{QWEATHER_API_BASE}/warning/now"
    params = {
        "location": location,
        "lang": "zh",
        "key": QWEATHER_API_KEY
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=params, timeout=30.0)
            print(f"API Key 方式 - 状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"API Key 方式 - 响应码: {data.get('code')}")
            else:
                print(f"API Key 方式 - 错误: {response.text}")
                
        except Exception as e:
            print(f"API Key 方式 - 异常: {e}")

async def test_with_bearer_token(location: str):
    """使用 Bearer Token（如果配置了的话）"""
    
    # 检查是否有 JWT Token 配置
    jwt_token = os.getenv("QWEATHER_JWT_TOKEN")
    
    if not jwt_token:
        print("未配置 JWT Token，跳过此测试")
        return
    
    url = f"{QWEATHER_API_BASE}/warning/now"
    params = {
        "location": location,
        "lang": "zh"
    }
    headers = {
        "Authorization": f"Bearer {jwt_token}"
    }
    
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get(url, params=params, headers=headers, timeout=30.0)
            print(f"Bearer Token 方式 - 状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"Bearer Token 方式 - 响应码: {data.get('code')}")
            else:
                print(f"Bearer Token 方式 - 错误: {response.text}")
                
        except Exception as e:
            print(f"Bearer Token 方式 - 异常: {e}")

def check_environment():
    """检查环境配置"""
    
    print("=== 环境配置检查 ===")
    print(f"QWEATHER_API_BASE: {QWEATHER_API_BASE}")
    print(f"QWEATHER_API_KEY: {QWEATHER_API_KEY}")
    
    if not QWEATHER_API_BASE:
        print("❌ 缺少 QWEATHER_API_BASE 环境变量")
        return False
    
    if not QWEATHER_API_KEY:
        print("❌ 缺少 QWEATHER_API_KEY 环境变量")
        return False
    
    print("✅ 环境变量配置正常")
    return True

if __name__ == "__main__":
    if not check_environment():
        sys.exit(1)
    
    asyncio.run(test_qweather_api())
