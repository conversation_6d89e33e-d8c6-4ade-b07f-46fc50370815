#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试参数验证功能，模拟 MCP 工具调用
"""

import asyncio
import sys
from pathlib import Path
from unittest.mock import patch, AsyncMock

# 添加服务器路径到 Python 路径
sys.path.append(str(Path(__file__).parent))

from server.weather_server import get_daily_forecast, get_weather_warning

async def test_parameter_validation():
    """测试参数验证功能"""
    
    print("=== 测试参数验证功能 ===\n")
    
    # 模拟 API 请求，避免实际网络调用
    with patch('server.weather_server.make_qweather_request', new_callable=AsyncMock) as mock_request:
        # 设置模拟返回值
        mock_request.return_value = {
            "code": "200",
            "daily": [
                {
                    "fxDate": "2025-08-02",
                    "tempMax": "30",
                    "tempMin": "20",
                    "textDay": "晴",
                    "textNight": "晴",
                    "windDirDay": "北风",
                    "windScaleDay": "3",
                    "windSpeedDay": "15",
                    "windDirNight": "北风",
                    "windScaleNight": "2",
                    "windSpeedNight": "10",
                    "humidity": "60",
                    "precip": "0.0",
                    "uvIndex": "8",
                    "vis": "25",
                    "sunrise": "06:00",
                    "sunset": "19:30"
                }
            ],
            "warning": []
        }
        
        # 测试 1: 不传递任何参数（应该使用默认值）
        print("测试 1: get_daily_forecast() - 不传递任何参数")
        try:
            result = await get_daily_forecast()
            print("✅ 成功：使用默认参数调用")
            print(f"调用参数: location='101010100', days=3")
            # 验证是否使用了默认参数
            mock_request.assert_called_with("weather/3d", {"location": "101010100", "lang": "zh"})
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        print("\n" + "="*50 + "\n")
        
        # 测试 2: 只传递 days 参数
        print("测试 2: get_daily_forecast(days=7) - 只传递 days 参数")
        try:
            result = await get_daily_forecast(days=7)
            print("✅ 成功：location 使用默认值，days 使用指定值")
            print(f"调用参数: location='101010100', days=7")
            # 验证参数
            mock_request.assert_called_with("weather/7d", {"location": "101010100", "lang": "zh"})
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        print("\n" + "="*50 + "\n")
        
        # 测试 3: 传递所有参数
        print("测试 3: get_daily_forecast(location='116.41,39.92', days=10) - 传递所有参数")
        try:
            result = await get_daily_forecast(location="116.41,39.92", days=10)
            print("✅ 成功：使用指定的所有参数")
            print(f"调用参数: location='116.41,39.92', days=10")
            # 验证参数
            mock_request.assert_called_with("weather/10d", {"location": "116.41,39.92", "lang": "zh"})
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        print("\n" + "="*50 + "\n")
        
        # 测试 4: 测试 get_weather_warning 默认参数
        print("测试 4: get_weather_warning() - 不传递任何参数")
        try:
            result = await get_weather_warning()
            print("✅ 成功：使用默认参数调用")
            print(f"调用参数: location='101010100'")
            # 验证参数
            mock_request.assert_called_with("warning/now", {"location": "101010100", "lang": "zh"})
        except Exception as e:
            print(f"❌ 失败: {e}")
        
        print("\n" + "="*50 + "\n")
        
        # 测试 5: 测试参数类型转换
        print("测试 5: get_daily_forecast(location=101010100, days='3') - 测试类型转换")
        try:
            result = await get_daily_forecast(location=101010100, days="3")
            print("✅ 成功：参数类型自动转换")
            print(f"调用参数: location=101010100 (int->str), days='3' (str->int)")
            # 验证参数转换
            mock_request.assert_called_with("weather/3d", {"location": "101010100", "lang": "zh"})
        except Exception as e:
            print(f"❌ 失败: {e}")

def test_mcp_tool_call_simulation():
    """模拟 MCP 工具调用场景"""
    
    print("\n" + "="*60)
    print("=== 模拟 MCP 工具调用场景 ===\n")
    
    # 模拟原始问题：只传递 days 参数，不传递 location 参数
    print("模拟原始问题场景:")
    print("客户端发送: {'days': 3}")
    print("期望行为: 使用 location 默认值 '101010100'")
    
    # 这种调用方式现在应该可以工作
    async def simulate_mcp_call():
        with patch('server.weather_server.make_qweather_request', new_callable=AsyncMock) as mock_request:
            mock_request.return_value = {"code": "200", "daily": [{"fxDate": "2025-08-02"}]}
            
            try:
                # 模拟只传递 days 参数的调用
                result = await get_daily_forecast(days=3)
                print("✅ 修复成功：现在可以只传递 days 参数")
                return True
            except Exception as e:
                print(f"❌ 仍然失败: {e}")
                return False
    
    success = asyncio.run(simulate_mcp_call())
    
    if success:
        print("\n🎉 问题已解决！")
        print("现在 get_daily_forecast 工具可以:")
        print("- 不传递任何参数（使用所有默认值）")
        print("- 只传递 days 参数（location 使用默认值）")
        print("- 传递所有参数（使用指定值）")
    else:
        print("\n❌ 问题仍然存在，需要进一步调试")

if __name__ == "__main__":
    asyncio.run(test_parameter_validation())
    test_mcp_tool_call_simulation()
