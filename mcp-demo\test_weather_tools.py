#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试天气工具的参数验证和调用
"""

import asyncio
import sys
import os
from pathlib import Path

# 添加服务器路径到 Python 路径
sys.path.append(str(Path(__file__).parent))

from server.weather_server import get_daily_forecast, get_weather_warning

async def test_weather_tools():
    """测试天气工具的各种调用方式"""
    
    print("=== 测试天气工具 ===\n")
    
    # 测试 1: 使用默认参数调用 get_daily_forecast
    print("测试 1: 调用 get_daily_forecast（使用默认参数）")
    try:
        result = await get_daily_forecast()
        print("✅ 成功调用，使用默认参数")
        print(f"结果长度: {len(result)} 字符")
    except Exception as e:
        print(f"❌ 调用失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试 2: 使用指定参数调用 get_daily_forecast
    print("测试 2: 调用 get_daily_forecast（指定参数）")
    try:
        result = await get_daily_forecast(location="101010100", days=7)
        print("✅ 成功调用，使用指定参数")
        print(f"结果长度: {len(result)} 字符")
    except Exception as e:
        print(f"❌ 调用失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试 3: 使用默认参数调用 get_weather_warning
    print("测试 3: 调用 get_weather_warning（使用默认参数）")
    try:
        result = await get_weather_warning()
        print("✅ 成功调用，使用默认参数")
        print(f"结果长度: {len(result)} 字符")
    except Exception as e:
        print(f"❌ 调用失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试 4: 使用指定参数调用 get_weather_warning
    print("测试 4: 调用 get_weather_warning（指定参数）")
    try:
        result = await get_weather_warning(location="101010100")
        print("✅ 成功调用，使用指定参数")
        print(f"结果长度: {len(result)} 字符")
    except Exception as e:
        print(f"❌ 调用失败: {e}")
    
    print("\n" + "="*50 + "\n")
    
    # 测试 5: 测试参数验证（无效的 days 值）
    print("测试 5: 测试参数验证（days=50，超出范围）")
    try:
        result = await get_daily_forecast(location="101010100", days=50)
        print("⚠️  调用成功，但应该被验证拦截")
        print(f"结果长度: {len(result)} 字符")
    except Exception as e:
        print(f"✅ 正确拦截无效参数: {e}")

if __name__ == "__main__":
    # 检查环境变量
    if not os.getenv("QWEATHER_API_KEY"):
        print("⚠️  警告: 未设置 QWEATHER_API_KEY 环境变量")
        print("工具调用可能会失败，但参数验证仍然可以测试")
        print()
    
    asyncio.run(test_weather_tools())
